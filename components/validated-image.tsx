"use client"

import { useState } from "react"
import { OptimizedImage } from "@/components/optimized-image"

interface ValidatedImageProps {
  src: string
  alt: string
  onError?: () => void
  className?: string
  aspectRatio?: "square" | "video" | "portrait" | "wide" | "auto"
  fill?: boolean
  priority?: boolean
  sizes?: string
  fallbackSrc?: string
  [key: string]: any // Allow other props to pass through
}

/**
 * Enhanced OptimizedImage component that handles validation errors
 * Calls onError when the image fails to load, allowing for URL refresh
 */
export function ValidatedImage({
  src,
  alt,
  onError,
  className = "",
  fallbackSrc,
  ...props
}: ValidatedImageProps) {
  const [hasError, setHasError] = useState(false)
  const [isRetrying, setIsRetrying] = useState(false)

  const handleError = async () => {
    if (hasError || isRetrying) {
      // Already handled or retrying, don't trigger again
      return
    }

    setHasError(true)
    
    if (onError) {
      setIsRetrying(true)
      try {
        await onError()
        // Reset error state after successful retry
        setHasError(false)
      } catch (error) {
        console.error("Error during image validation retry:", error)
      } finally {
        setIsRetrying(false)
      }
    }
  }

  // Use fallback if we have an error and no retry is in progress
  const imageSrc = hasError && !isRetrying && fallbackSrc ? fallbackSrc : src

  return (
    <OptimizedImage
      src={imageSrc}
      alt={alt}
      className={`${className} ${isRetrying ? "opacity-75 transition-opacity" : ""}`}
      onError={handleError}
      {...props}
    />
  )
}

/**
 * Usage example:
 * 
 * const { imageUrl, handleImageError } = useValidatedTripImageUrl(trip)
 * 
 * <ValidatedImage
 *   src={imageUrl}
 *   alt={trip.destination}
 *   onError={handleImageError}
 *   fallbackSrc="/placeholder.svg"
 * />
 */
