"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Clock, Star, Users } from "lucide-react"
import { type Trip } from "@/lib/domains/trip/trip.types"
import { OptimizedImage } from "@/components/optimized-image"
import { Skeleton } from "@/components/ui/skeleton"
import { useValidatedTripImageUrl } from "@/lib/hooks/use-validated-image-url"

interface EnhancedTripCardProps {
  trip: Trip
  isPast: boolean
  enableImageValidation?: boolean // Optional prop to control validation
}

/**
 * Enhanced Trip Card with automatic image URL validation and refresh
 * 
 * This component demonstrates how to integrate the new image validation system:
 * 1. Uses useValidatedTripImageUrl hook instead of getTripImageUrl
 * 2. Shows validation state with visual feedback
 * 3. Automatically refreshes expired Google Places image URLs
 */
export function EnhancedTripCard({ 
  trip, 
  isPast, 
  enableImageValidation = true 
}: EnhancedTripCardProps) {
  const [imageLoading, setImageLoading] = useState(true)
  
  // Use the new validation hook
  const { 
    imageUrl, 
    isValidating, 
    hasValidated 
  } = useValidatedTripImageUrl(trip, "400x200", enableImageValidation)

  // Format date safely
  const formatDate = (timestamp: any) => {
    if (!timestamp) return ""

    try {
      return typeof timestamp.toDate === "function"
        ? timestamp.toDate().toLocaleDateString()
        : new Date(timestamp).toLocaleDateString()
    } catch (error) {
      console.error("Error formatting date:", error)
      return "Invalid date"
    }
  }

  return (
    <Link href={`/trips/${trip.id}`}>
      <Card className="h-full hover:shadow-md transition-shadow">
        <div className="aspect-video relative overflow-hidden rounded-t-lg">
          {imageLoading && <Skeleton className="absolute inset-0 z-10" />}
          
          {/* Optional: Show validation indicator */}
          {isValidating && (
            <div className="absolute top-2 right-2 z-20">
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" 
                   title="Validating image..." />
            </div>
          )}
          
          <OptimizedImage
            src={imageUrl}
            alt={trip.destination}
            aspectRatio="video"
            className={`rounded-t-lg transition-opacity duration-300 ${
              isValidating ? "opacity-75" : "opacity-100"
            }`}
            priority
            onLoad={() => setImageLoading(false)}
            fallbackSrc="/placeholder.svg?height=200&width=400"
          />
          
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 z-10">
            <h3 className="text-white font-bold">{trip.destination}</h3>
            <p className="text-white/80 text-sm">
              {formatDate(trip.startDate)} - {formatDate(trip.endDate)}
            </p>
          </div>
        </div>
        
        <CardContent className="pt-4">
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                {trip.memberIds?.length || 0} members
              </span>
            </div>
            {isPast && (
              <div className="flex items-center space-x-1">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm text-muted-foreground">Past trip</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>
              {trip.status === "planning" && "Planning"}
              {trip.status === "active" && "Active"}
              {trip.status === "completed" && "Completed"}
            </span>
            
            {/* Optional: Show validation status for debugging */}
            {process.env.NODE_ENV === "development" && (
              <span className="ml-auto text-xs">
                {!hasValidated && "⏳"}
                {hasValidated && !isValidating && "✅"}
                {isValidating && "🔄"}
              </span>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}

/**
 * Usage Examples:
 * 
 * // Basic usage (validation enabled by default)
 * <EnhancedTripCard trip={trip} isPast={false} />
 * 
 * // Disable validation if needed
 * <EnhancedTripCard trip={trip} isPast={false} enableImageValidation={false} />
 * 
 * // The component will:
 * 1. Show the image immediately using cached/fallback URLs
 * 2. Validate the image URL in the background
 * 3. Update the image if a refreshed URL is available
 * 4. Show subtle visual feedback during validation
 */
