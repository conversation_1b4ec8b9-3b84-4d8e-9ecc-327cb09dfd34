"use client"

import { useState, useEffect } from "react"
import { Trip } from "@/lib/domains/trip/trip.types"
import { CachedTripSuggestion } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.service"
import { 
  getTripImageUrl, 
  getSuggestionImageUrl,
  getTripImageUrlWithValidation,
  getSuggestionImageUrlWithValidation 
} from "@/lib/utils/trip-image-utils"

/**
 * Hook to get a validated image URL for a trip
 * Returns the immediate URL first, then validates and updates if needed
 */
export function useValidatedTripImageUrl(
  trip: Trip | null,
  placeholderSize: string = "400x1200",
  enableValidation: boolean = true
) {
  const [imageUrl, setImageUrl] = useState<string>("")
  const [isValidating, setIsValidating] = useState(false)
  const [hasValidated, setHasValidated] = useState(false)

  useEffect(() => {
    if (!trip) {
      setImageUrl("")
      setHasValidated(false)
      return
    }

    // Set immediate URL first (synchronous)
    const immediateUrl = getTripImageUrl(trip, placeholderSize)
    setImageUrl(immediateUrl)
    setHasValidated(false)

    // If validation is enabled, validate the URL in the background
    if (enableValidation) {
      setIsValidating(true)
      
      getTripImageUrlWithValidation(trip, placeholderSize)
        .then((validatedUrl) => {
          // Only update if the URL actually changed
          if (validatedUrl !== immediateUrl) {
            setImageUrl(validatedUrl)
          }
          setHasValidated(true)
        })
        .catch((error) => {
          console.error("Error validating trip image URL:", error)
          setHasValidated(true)
        })
        .finally(() => {
          setIsValidating(false)
        })
    } else {
      setHasValidated(true)
    }
  }, [trip?.id, trip?.locationThumbnail, trip?.image, placeholderSize, enableValidation])

  return {
    imageUrl,
    isValidating,
    hasValidated,
  }
}

/**
 * Hook to get a validated image URL for a suggestion
 * Returns the immediate URL first, then validates and updates if needed
 */
export function useValidatedSuggestionImageUrl(
  suggestion: CachedTripSuggestion | null,
  placeholderSize: string = "300x150",
  enableValidation: boolean = true
) {
  const [imageUrl, setImageUrl] = useState<string>("")
  const [isValidating, setIsValidating] = useState(false)
  const [hasValidated, setHasValidated] = useState(false)

  useEffect(() => {
    if (!suggestion) {
      setImageUrl("")
      setHasValidated(false)
      return
    }

    // Set immediate URL first (synchronous)
    const immediateUrl = getSuggestionImageUrl(suggestion, placeholderSize)
    setImageUrl(immediateUrl)
    setHasValidated(false)

    // If validation is enabled, validate the URL in the background
    if (enableValidation) {
      setIsValidating(true)
      
      getSuggestionImageUrlWithValidation(suggestion, placeholderSize)
        .then((validatedUrl) => {
          // Only update if the URL actually changed
          if (validatedUrl !== immediateUrl) {
            setImageUrl(validatedUrl)
          }
          setHasValidated(true)
        })
        .catch((error) => {
          console.error("Error validating suggestion image URL:", error)
          setHasValidated(true)
        })
        .finally(() => {
          setIsValidating(false)
        })
    } else {
      setHasValidated(true)
    }
  }, [suggestion?.id, suggestion?.image, suggestion?.destination, placeholderSize, enableValidation])

  return {
    imageUrl,
    isValidating,
    hasValidated,
  }
}

/**
 * Hook for generic image URL validation
 * Useful for any image URL that might need validation and refresh
 */
export function useImageUrlValidation(
  initialUrl: string,
  enableValidation: boolean = true
) {
  const [imageUrl, setImageUrl] = useState<string>(initialUrl)
  const [isValidating, setIsValidating] = useState(false)
  const [hasValidated, setHasValidated] = useState(false)
  const [isAccessible, setIsAccessible] = useState<boolean | null>(null)

  useEffect(() => {
    setImageUrl(initialUrl)
    setHasValidated(false)
    setIsAccessible(null)

    if (!initialUrl || !enableValidation) {
      setHasValidated(true)
      return
    }

    // Import the validation function dynamically to avoid SSR issues
    import("@/lib/google-places").then(({ isImageUrlAccessible }) => {
      setIsValidating(true)
      
      isImageUrlAccessible(initialUrl)
        .then((accessible) => {
          setIsAccessible(accessible)
          setHasValidated(true)
        })
        .catch((error) => {
          console.error("Error validating image URL:", error)
          setIsAccessible(false)
          setHasValidated(true)
        })
        .finally(() => {
          setIsValidating(false)
        })
    })
  }, [initialUrl, enableValidation])

  return {
    imageUrl,
    isValidating,
    hasValidated,
    isAccessible,
  }
}
