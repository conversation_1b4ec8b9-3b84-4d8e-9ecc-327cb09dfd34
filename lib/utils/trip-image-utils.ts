import { Trip } from "@/lib/domains/trip/trip.types"
import { CachedTripSuggestion } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.service"
import {
  validateAndRefreshImageUrl,
  findCachedLocationImageWithAttribution,
} from "@/lib/google-places"

/**
 * Generates a fresh Google Places image URL using server-side API
 */
async function generateGooglePlaceImageUrl(
  photoReference: string,
  placeId: string
): Promise<string | null> {
  try {
    const response = await fetch(
      `/api/images/google-places-url?photoReference=${encodeURIComponent(photoReference)}&placeId=${encodeURIComponent(placeId)}`
    )
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    return data.imageUrl
  } catch (error) {
    console.error("Error generating Google Places image URL:", error)
    return null
  }
}

/**
 * Helper function to get the image URL from a trip
 * Uses GooglePlaceImage if available, otherwise falls back to locationThumbnail → image → placeholder
 */
export function getTripImageUrl(trip: Trip, placeholderSize: string = "400x1200"): string {
  // Use GooglePlaceImage if available (but this requires async, so we'll use a different approach)
  // For now, use the fallback chain - we'll implement async image loading in components

  // Fallback chain: locationThumbnail → image → placeholder
  return (
    trip.locationThumbnail ||
    trip.image ||
    `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`
  )
}

/**
 * Async version that can generate fresh URLs from GooglePlaceImage data
 */
export async function getTripImageUrlAsync(
  trip: Trip,
  placeholderSize: string = "400x1200"
): Promise<string> {
  // Try to generate fresh URL from GooglePlaceImage if available
  if (trip.googlePlaceImage?.photoReference && trip.googlePlaceImage?.placeId) {
    const freshUrl = await generateGooglePlaceImageUrl(
      trip.googlePlaceImage.photoReference,
      trip.googlePlaceImage.placeId
    )
    if (freshUrl) {
      return freshUrl
    }
  }

  // Fallback chain: locationThumbnail → image → placeholder
  return (
    trip.locationThumbnail ||
    trip.image ||
    `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`
  )
}

/**
 * Helper function to get the image URL from a trip suggestion
 * Uses GooglePlaceImage if available, otherwise falls back to direct image URL
 */
export function getSuggestionImageUrl(
  suggestion: CachedTripSuggestion,
  placeholderSize: string = "300x150"
): string {
  // For now, use the direct image URL - async version available for fresh URLs

  // Fallback to direct image URL
  return (
    suggestion.image ||
    `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`
  )
}

/**
 * Async version that can generate fresh URLs from GooglePlaceImage data
 */
export async function getSuggestionImageUrlAsync(
  suggestion: CachedTripSuggestion,
  placeholderSize: string = "300x150"
): Promise<string> {
  // Try to generate fresh URL from GooglePlaceImage if available
  if (suggestion.googlePlaceImage?.photoReference && suggestion.googlePlaceImage?.placeId) {
    const freshUrl = await generateGooglePlaceImageUrl(
      suggestion.googlePlaceImage.photoReference,
      suggestion.googlePlaceImage.placeId
    )
    if (freshUrl) {
      return freshUrl
    }
  }

  // Fallback to direct image URL
  return (
    suggestion.image ||
    `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`
  )
}

/**
 * Helper function for trip suggestion details (with different interface)
 */
export function getTripSuggestionImageUrl(
  suggestion: {
    image: string
    googlePlaceImage?: {
      photoReference: string
      placeId: string
    }
  },
  placeholderSize: string = "600x300"
): string {
  // For now, use the direct image URL until we implement proper server-side URL generation
  // TODO: Implement server-side URL generation for GooglePlaceImage data

  // Fallback to direct image URL
  return (
    suggestion.image ||
    `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`
  )
}

/**
 * Enhanced version that validates and refreshes trip image URLs
 * Checks if the current URL is accessible and refreshes if needed
 * @param trip The trip object
 * @param placeholderSize The placeholder size if no image is available
 * @returns Promise<string> The validated or refreshed image URL
 */
export async function getTripImageUrlWithValidation(
  trip: Trip,
  placeholderSize: string = "400x1200"
): Promise<string> {
  try {
    // If we have a locationThumbnail, try to find it in the cache and validate it
    if (trip.locationThumbnail && trip.destination) {
      const cachedImage = await findCachedLocationImageWithAttribution(trip.destination)
      if (cachedImage && cachedImage.imageUrl === trip.locationThumbnail) {
        // This image is in our cache, validate and potentially refresh it
        const validatedUrl = await validateAndRefreshImageUrl(cachedImage)
        return validatedUrl
      }
    }

    // Fall back to the regular logic
    return getTripImageUrl(trip, placeholderSize)
  } catch (error) {
    console.error("Error validating trip image URL:", error)
    // Fall back to the regular logic if validation fails
    return getTripImageUrl(trip, placeholderSize)
  }
}

/**
 * Enhanced version that validates and refreshes suggestion image URLs
 * Checks if the current URL is accessible and refreshes if needed
 * @param suggestion The suggestion object
 * @param placeholderSize The placeholder size if no image is available
 * @returns Promise<string> The validated or refreshed image URL
 */
export async function getSuggestionImageUrlWithValidation(
  suggestion: CachedTripSuggestion,
  placeholderSize: string = "300x150"
): Promise<string> {
  try {
    // If we have an image URL, try to find it in the cache and validate it
    if (suggestion.image && suggestion.destination) {
      const cachedImage = await findCachedLocationImageWithAttribution(suggestion.destination)
      if (cachedImage && cachedImage.imageUrl === suggestion.image) {
        // This image is in our cache, validate and potentially refresh it
        const validatedUrl = await validateAndRefreshImageUrl(cachedImage)
        return validatedUrl
      }
    }

    // Fall back to the regular logic
    return getSuggestionImageUrl(suggestion, placeholderSize)
  } catch (error) {
    console.error("Error validating suggestion image URL:", error)
    // Fall back to the regular logic if validation fails
    return getSuggestionImageUrl(suggestion, placeholderSize)
  }
}
