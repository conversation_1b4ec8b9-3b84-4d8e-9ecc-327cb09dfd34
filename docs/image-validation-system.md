# Image URL Validation and Refresh System

## Overview

This system automatically detects when Google Places image URLs return 4XX errors and refreshes them using stored `photoReference` and `placeId` data. This ensures that images remain accessible even when Google's URLs expire or change.

## How It Works

1. **Image URL Validation**: When an image URL is accessed, the system checks if it returns a 4XX error
2. **Automatic Refresh**: If a 4XX error is detected and we have `googlePlaceImage` data, the system generates a fresh URL
3. **Cache Update**: The new URL is automatically saved back to the `locationImages` collection
4. **Seamless Fallback**: If refresh fails, the original URL is still used (it might work for the user)

## Database Schema Updates

The `LocationImage` interface now includes:

```typescript
interface LocationImage {
  id?: string
  location: string
  imageUrl: string
  source: "google" | "custom"
  sourceId?: string
  createdAt?: any
  updatedAt?: any  // NEW: Track when URLs are refreshed
  attribution?: {
    name: string
    photoReference: string
  }
  googlePlaceImage?: {  // NEW: Store refresh data
    photoReference: string
    placeId: string
  }
}
```

## Core Functions

### `isImageUrlAccessible(imageUrl: string): Promise<boolean>`
Checks if an image URL returns a 4XX error.

### `refreshGooglePlaceImageUrl(photoReference: string, placeId: string): Promise<string | null>`
Generates a fresh Google Places image URL using the server-side API.

### `validateAndRefreshImageUrl(cachedImage: LocationImage): Promise<string>`
Main validation function that checks accessibility and refreshes if needed.

## Usage Examples

### 1. Using the Enhanced Utility Functions

```typescript
import { getTripImageUrlWithValidation, getSuggestionImageUrlWithValidation } from "@/lib/utils/trip-image-utils"

// For trips
const validatedTripImageUrl = await getTripImageUrlWithValidation(trip, "400x200")

// For suggestions
const validatedSuggestionImageUrl = await getSuggestionImageUrlWithValidation(suggestion, "300x150")
```

### 2. Using the React Hooks

```typescript
import { useValidatedTripImageUrl, useValidatedSuggestionImageUrl } from "@/lib/hooks/use-validated-image-url"

function TripCard({ trip }) {
  const { imageUrl, isValidating, hasValidated } = useValidatedTripImageUrl(trip, "400x200")
  
  return (
    <OptimizedImage
      src={imageUrl}
      alt={trip.destination}
      // Optional: Show loading state during validation
      className={isValidating ? "opacity-75" : ""}
    />
  )
}
```

### 3. Manual Validation

```typescript
import { validateAndRefreshImageUrl, findCachedLocationImageWithAttribution } from "@/lib/google-places"

const cachedImage = await findCachedLocationImageWithAttribution("Paris, France")
if (cachedImage) {
  const validatedUrl = await validateAndRefreshImageUrl(cachedImage)
  // Use validatedUrl
}
```

## Integration Guide

### For Existing Components

1. **Immediate Integration** (No code changes needed):
   - The `getLocationImage()` and `getLocationImageByPlaceId()` functions now automatically validate and refresh URLs
   - All new images saved to cache include `googlePlaceImage` data

2. **Enhanced Integration** (Recommended):
   - Replace `getTripImageUrl()` with `useValidatedTripImageUrl()` hook
   - Replace `getSuggestionImageUrl()` with `useValidatedSuggestionImageUrl()` hook

### Example: Updating a Trip Card Component

**Before:**
```typescript
import { getTripImageUrl } from "@/lib/utils/trip-image-utils"

function TripCard({ trip }) {
  return (
    <OptimizedImage
      src={getTripImageUrl(trip, "400x200")}
      alt={trip.destination}
    />
  )
}
```

**After:**
```typescript
import { useValidatedTripImageUrl } from "@/lib/hooks/use-validated-image-url"

function TripCard({ trip }) {
  const { imageUrl, isValidating } = useValidatedTripImageUrl(trip, "400x200")
  
  return (
    <OptimizedImage
      src={imageUrl}
      alt={trip.destination}
      className={isValidating ? "opacity-75 transition-opacity" : ""}
    />
  )
}
```

## Performance Considerations

1. **Lazy Validation**: Validation only happens when images are actually displayed
2. **Caching**: Validated URLs are cached to avoid repeated validation
3. **Background Processing**: Validation happens asynchronously without blocking UI
4. **Fallback Strategy**: Original URLs are used if validation fails

## Configuration

### Disable Validation (if needed)
```typescript
// Disable validation for a specific component
const { imageUrl } = useValidatedTripImageUrl(trip, "400x200", false)

// Or use the original functions
const imageUrl = getTripImageUrl(trip, "400x200")
```

### Server-Side Usage
The validation functions work on both client and server side, but the React hooks are client-side only.

## Monitoring

The system logs validation attempts and results:
- `Image URL not accessible for {location}, attempting to refresh...`
- `Successfully refreshed image URL for {location}`
- `Could not refresh image URL for {location}, using original URL`

## Future Enhancements

1. **Batch Validation**: Validate multiple images in parallel
2. **Proactive Refresh**: Periodically refresh URLs before they expire
3. **Analytics**: Track validation success rates and common failure patterns
4. **Retry Logic**: Implement exponential backoff for failed validations
